#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Streamlit 1.40.1 版本修复验证脚本
"""
import pandas as pd
import numpy as np

def create_test_data():
    """创建测试数据"""
    np.random.seed(42)
    
    # 创建包含各种数据类型的测试数据
    data = {
        '姓名': ['张三', '李四', '王五', '赵六', '钱七', '孙八'] * 5,
        '年龄': np.random.randint(20, 60, 30),
        '城市': np.random.choice(['北京', '上海', '广州', '深圳', '杭州'], 30),
        '部门': np.random.choice(['技术', '销售', '管理', '财务'], 30),
        '薪资': np.random.normal(10000, 3000, 30).astype(int),
        '工作年限': np.random.randint(1, 20, 30),
        '绩效得分': np.random.normal(80, 15, 30),
        '等级': np.random.choice(['初级', '中级', '高级', '专家'], 30),
        '状态': np.random.choice(['在职', '离职'], 30, p=[0.8, 0.2]),
        '项目数': np.random.randint(0, 10, 30),
    }
    
    df = pd.DataFrame(data)
    return df

def analyze_data_for_charts(df):
    """分析数据，为图表测试提供建议"""
    print("=== 数据分析报告 ===")
    print(f"数据形状: {df.shape}")
    print(f"总行数: {len(df)}")
    
    # 分析列类型
    numeric_cols = df.select_dtypes(include=['number']).columns.tolist()
    categorical_cols = df.select_dtypes(include=['object']).columns.tolist()
    
    print(f"\n数值列 ({len(numeric_cols)}): {numeric_cols}")
    print(f"分类列 ({len(categorical_cols)}): {categorical_cols}")
    
    # 分析唯一值数量
    print("\n=== 唯一值分析 ===")
    for col in df.columns:
        unique_count = df[col].nunique()
        print(f"{col}: {unique_count} 个唯一值")
        if unique_count <= 20:
            print(f"  ✅ 适合饼图分类 (≤20)")
        elif unique_count <= 50:
            print(f"  ✅ 适合帕累托图分类 (≤50)")
        else:
            print(f"  ❌ 唯一值过多，不适合分类图表")
    
    return numeric_cols, categorical_cols

def test_pivot_combinations(df):
    """测试透视表组合"""
    print("\n=== 透视表测试建议 ===")
    
    # 推荐的透视表组合
    recommendations = [
        {
            "描述": "基础计数透视表",
            "行索引": ["部门"],
            "列索引": [],
            "值列": [],
            "聚合": "count"
        },
        {
            "描述": "部门-城市交叉分析",
            "行索引": ["部门"],
            "列索引": ["城市"],
            "值列": [],
            "聚合": "count"
        },
        {
            "描述": "薪资统计分析",
            "行索引": ["部门"],
            "列索引": ["等级"],
            "值列": ["薪资"],
            "聚合": "mean"
        },
        {
            "描述": "绩效得分分析",
            "行索引": ["等级"],
            "列索引": [],
            "值列": ["绩效得分"],
            "聚合": "mean"
        }
    ]
    
    for i, rec in enumerate(recommendations, 1):
        print(f"\n{i}. {rec['描述']}")
        print(f"   行索引: {rec['行索引']}")
        print(f"   列索引: {rec['列索引'] if rec['列索引'] else '无'}")
        print(f"   值列: {rec['值列'] if rec['值列'] else '无'}")
        print(f"   聚合函数: {rec['聚合']}")

def test_chart_combinations(df, numeric_cols, categorical_cols):
    """测试图表组合"""
    print("\n=== 图表测试建议 ===")
    
    # 自动检测适合的分类列
    auto_categorical = []
    for col in df.columns:
        if df[col].nunique() <= 20:
            auto_categorical.append(col)
    
    chart_tests = [
        {
            "图表类型": "柱状图",
            "X轴": auto_categorical[0] if auto_categorical else "部门",
            "Y轴": numeric_cols[0] if numeric_cols else "薪资",
            "说明": "基础柱状图测试"
        },
        {
            "图表类型": "折线图", 
            "X轴": "工作年限",
            "Y轴": "薪资",
            "说明": "趋势分析"
        },
        {
            "图表类型": "散点图",
            "X轴": "工作年限",
            "Y轴": "薪资",
            "说明": "相关性分析"
        },
        {
            "图表类型": "饼图",
            "X轴": auto_categorical[0] if auto_categorical else "部门",
            "Y轴": "计数",
            "说明": f"分类分布 (检测到 {len(auto_categorical)} 个适合的分类列)"
        },
        {
            "图表类型": "帕累托图",
            "X轴": auto_categorical[0] if auto_categorical else "部门", 
            "Y轴": "计数",
            "说明": "80/20分析"
        }
    ]
    
    for i, test in enumerate(chart_tests, 1):
        print(f"\n{i}. {test['图表类型']}")
        print(f"   X轴: {test['X轴']}")
        print(f"   Y轴: {test['Y轴']}")
        print(f"   说明: {test['说明']}")

def main():
    """主函数"""
    print("Streamlit 1.40.1 版本修复验证")
    print("=" * 50)
    
    # 创建测试数据
    df = create_test_data()
    
    # 保存测试数据
    df.to_csv('streamlit_test_data.csv', index=False, encoding='utf-8-sig')
    print("✅ 测试数据已保存为 streamlit_test_data.csv")
    
    # 分析数据
    numeric_cols, categorical_cols = analyze_data_for_charts(df)
    
    # 测试建议
    test_pivot_combinations(df)
    test_chart_combinations(df, numeric_cols, categorical_cols)
    
    print("\n=== 修复验证要点 ===")
    print("1. ✅ 页面卡死问题")
    print("   - 移除了st.rerun()调用")
    print("   - 简化了状态管理")
    print("   - 优化了数值标签逻辑")
    
    print("\n2. ✅ 透视表错误修复")
    print("   - 重写了create_pivot_table_safe函数")
    print("   - 使用groupby方式处理count聚合")
    print("   - 严格的参数类型检查")
    
    print("\n3. ✅ 图表分类列自动检测")
    print("   - 饼图: 自动检测唯一值≤20的列")
    print("   - 帕累托图: 自动检测唯一值≤50的列")
    print("   - 智能提示和错误处理")
    
    print("\n=== 测试步骤 ===")
    print("1. 启动程序: python local_ai_fixedV0.83.py")
    print("2. 加载文件: streamlit_test_data.csv")
    print("3. 点击'数据预览'")
    print("4. 测试交互式图表可视化:")
    print("   - 尝试各种图表类型")
    print("   - 测试显示数值标签功能")
    print("   - 验证页面不卡死")
    print("5. 测试交互式数据透视表:")
    print("   - 尝试上述推荐的透视表组合")
    print("   - 验证不再出现'not 1-dimensional'错误")
    print("   - 确认透视表正确显示")
    
    print("\n=== 预期结果 ===")
    print("✅ 显示数值标签不导致页面卡死")
    print("✅ 图表切换流畅，无卡死现象")
    print("✅ 透视表正常生成，无Grouper错误")
    print("✅ 饼图和帕累托图自动检测分类列")
    print("✅ 错误处理友好，提供手动刷新提示")

if __name__ == "__main__":
    main()
