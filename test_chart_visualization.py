#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试独立的交互式图表可视化功能
"""
import pandas as pd
import tempfile
import os
from datetime import datetime

# 创建更丰富的测试数据
test_data = {
    '姓名': ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十', '郑十一', '王十二'],
    '年龄': [25, 30, 28, 35, 22, 29, 33, 26, 31, 27],
    '城市': ['北京', '上海', '广州', '深圳', '杭州', '成都', '武汉', '西安', '北京', '上海'],
    '薪资': [8000, 12000, 9500, 15000, 7000, 8500, 11000, 7500, 9000, 10500],
    '部门': ['技术', '销售', '技术', '管理', '销售', '技术', '管理', '销售', '技术', '管理'],
    '工作年限': [3, 8, 5, 12, 1, 6, 10, 4, 7, 5]
}
df = pd.DataFrame(test_data)

def create_enhanced_streamlit_app(df, title="数据预览"):
    """创建包含独立图表可视化功能的streamlit应用"""
    temp_dir = tempfile.gettempdir()
    session_id = "chart_test"
    app_file = os.path.join(temp_dir, f"streamlit_chart_{session_id}.py")
    data_file = os.path.join(temp_dir, f"data_chart_{session_id}.pkl")

    # 保存数据到临时文件
    df.to_pickle(data_file)

    # 创建streamlit应用代码
    app_code = f'''
import streamlit as st
import pandas as pd
import numpy as np
import os
import io
try:
    import plotly.express as px
    import plotly.graph_objects as go
    from plotly.subplots import make_subplots
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False
import matplotlib.pyplot as plt
import matplotlib
import base64
from io import BytesIO
from datetime import datetime

# 设置matplotlib后端
matplotlib.use('Agg')

st.set_page_config(
    page_title="{title}",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 加载数据
@st.cache_data
def load_data():
    return pd.read_pickle(r"{data_file}")

try:
    df = load_data()

    # 主标题
    st.title("📊 {title}")
    
    # 显示模块可用性警告
    if not PLOTLY_AVAILABLE:
        st.warning("⚠️ plotly模块未安装，部分交互式图表功能将不可用")
    
    st.markdown("---")

    # 数据概览
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("总行数", df.shape[0])
    with col2:
        st.metric("总列数", df.shape[1])
    with col3:
        st.metric("数值列", len(df.select_dtypes(include=['number']).columns))
    with col4:
        st.metric("文本列", len(df.select_dtypes(include=['object']).columns))

    # 显示数据
    st.subheader("📋 数据内容")
    st.dataframe(df, use_container_width=True)

    # 独立的交互式图表可视化功能
    st.markdown("---")
    st.subheader("📊 交互式图表可视化")
    
    if PLOTLY_AVAILABLE:
        # 创建图表配置区域
        chart_col1, chart_col2 = st.columns([1, 3])
        
        with chart_col1:
            st.write("**图表配置**")
            
            # 图表类型选择
            chart_types = [
                "柱状图", "折线图", "散点图", "面积图", "饼图", 
                "箱线图", "小提琴图", "直方图", "热力图", "相关性矩阵"
            ]
            selected_chart_type = st.selectbox("选择图表类型", chart_types)
            
            # 获取数值列和分类列
            numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
            categorical_cols = df.select_dtypes(include=['object', 'category']).columns.tolist()
            all_cols = df.columns.tolist()
            
            # 根据图表类型显示不同的配置选项
            x_axis = None
            y_axis = None
            color_by = None
            size_by = None
            
            if selected_chart_type in ["柱状图", "折线图", "面积图"]:
                x_axis = st.selectbox("X轴", all_cols)
                y_axis = st.selectbox("Y轴", numeric_cols if numeric_cols else all_cols)
                if len(categorical_cols) > 0:
                    color_by = st.selectbox("颜色分组 (可选)", ["无"] + categorical_cols)
                    color_by = None if color_by == "无" else color_by
                    
            elif selected_chart_type == "散点图":
                if len(numeric_cols) >= 2:
                    x_axis = st.selectbox("X轴", numeric_cols)
                    y_axis = st.selectbox("Y轴", [col for col in numeric_cols if col != x_axis])
                    if len(categorical_cols) > 0:
                        color_by = st.selectbox("颜色分组 (可选)", ["无"] + categorical_cols)
                        color_by = None if color_by == "无" else color_by
                    if len(numeric_cols) > 2:
                        size_by = st.selectbox("大小映射 (可选)", ["无"] + [col for col in numeric_cols if col not in [x_axis, y_axis]])
                        size_by = None if size_by == "无" else size_by
                else:
                    st.warning("散点图需要至少2个数值列")
                    
            elif selected_chart_type == "饼图":
                if len(categorical_cols) > 0:
                    x_axis = st.selectbox("分类列", categorical_cols)
                    if len(numeric_cols) > 0:
                        y_axis = st.selectbox("数值列 (可选)", ["计数"] + numeric_cols)
                        y_axis = None if y_axis == "计数" else y_axis
                else:
                    st.warning("饼图需要至少1个分类列")
                    
            elif selected_chart_type in ["箱线图", "小提琴图"]:
                if len(numeric_cols) > 0:
                    y_axis = st.selectbox("数值列", numeric_cols)
                    if len(categorical_cols) > 0:
                        x_axis = st.selectbox("分组列 (可选)", ["无"] + categorical_cols)
                        x_axis = None if x_axis == "无" else x_axis
                else:
                    st.warning(f"{{selected_chart_type}}需要至少1个数值列")
                    
            elif selected_chart_type == "直方图":
                if len(numeric_cols) > 0:
                    x_axis = st.selectbox("数值列", numeric_cols)
                    if len(categorical_cols) > 0:
                        color_by = st.selectbox("颜色分组 (可选)", ["无"] + categorical_cols)
                        color_by = None if color_by == "无" else color_by
                else:
                    st.warning("直方图需要至少1个数值列")
                    
            elif selected_chart_type == "热力图":
                if len(numeric_cols) >= 2:
                    st.info("将显示所有数值列的热力图")
                else:
                    st.warning("热力图需要至少2个数值列")
                    
            elif selected_chart_type == "相关性矩阵":
                if len(numeric_cols) >= 2:
                    st.info("将显示数值列之间的相关性")
                else:
                    st.warning("相关性矩阵需要至少2个数值列")
            
            # 通用配置选项
            st.write("**显示选项**")
            show_values = st.checkbox("显示数值标签", value=False)
            chart_height = st.slider("图表高度", 300, 800, 500)
            
        with chart_col2:
            st.write("**图表预览**")
            
            try:
                fig = None
                
                # 根据选择的图表类型生成图表
                if selected_chart_type == "柱状图" and x_axis and y_axis:
                    if color_by:
                        fig = px.bar(df, x=x_axis, y=y_axis, color=color_by, 
                                   title=f"{{y_axis}} 按 {{x_axis}} 分组")
                    else:
                        fig = px.bar(df, x=x_axis, y=y_axis, 
                                   title=f"{{y_axis}} 按 {{x_axis}}")
                    if show_values:
                        fig.update_traces(texttemplate='%{{y}}', textposition='outside')
                        
                elif selected_chart_type == "折线图" and x_axis and y_axis:
                    if color_by:
                        fig = px.line(df, x=x_axis, y=y_axis, color=color_by,
                                    title=f"{{y_axis}} 趋势图 (按 {{color_by}} 分组)")
                    else:
                        fig = px.line(df, x=x_axis, y=y_axis,
                                    title=f"{{y_axis}} 趋势图")
                    fig.update_traces(mode='lines+markers')
                    if show_values:
                        fig.update_traces(mode='lines+markers+text', texttemplate='%{{y}}')
                        
                elif selected_chart_type == "散点图" and x_axis and y_axis:
                    fig = px.scatter(df, x=x_axis, y=y_axis, color=color_by, size=size_by,
                                   title=f"{{y_axis}} vs {{x_axis}}")
                    
                elif selected_chart_type == "饼图" and x_axis:
                    if y_axis:
                        # 使用指定的数值列
                        pie_data = df.groupby(x_axis)[y_axis].sum().reset_index()
                        fig = px.pie(pie_data, values=y_axis, names=x_axis,
                                   title=f"{{x_axis}} 分布 ({{y_axis}})")
                    else:
                        # 使用计数
                        pie_data = df[x_axis].value_counts().reset_index()
                        pie_data.columns = [x_axis, 'count']
                        fig = px.pie(pie_data, values='count', names=x_axis,
                                   title=f"{{x_axis}} 分布 (计数)")
                    fig.update_traces(textposition='inside', textinfo='percent+label')
                    
                elif selected_chart_type == "相关性矩阵" and len(numeric_cols) >= 2:
                    # 计算相关性矩阵
                    corr_matrix = df[numeric_cols].corr()
                    fig = px.imshow(corr_matrix.values,
                                  x=corr_matrix.columns,
                                  y=corr_matrix.index,
                                  title="数值列相关性矩阵",
                                  color_continuous_scale='RdBu',
                                  text_auto=True)
                
                # 显示图表
                if fig:
                    fig.update_layout(height=chart_height)
                    st.plotly_chart(fig, use_container_width=True)
                    
                    # 提供图表下载
                    img_bytes = fig.to_image(format="png", width=1200, height=chart_height)
                    st.download_button(
                        label="📥 下载图表",
                        data=img_bytes,
                        file_name=f"{{selected_chart_type}}_{{datetime.now().strftime('%Y%m%d_%H%M%S')}}.png",
                        mime="image/png"
                    )
                else:
                    st.info("请配置图表参数以生成可视化")
                    
            except Exception as e:
                st.error(f"图表生成失败: {{str(e)}}")
                st.info("请检查选择的列是否适合当前图表类型")
    else:
        st.info("📊 交互式图表功能需要plotly模块支持，请安装: pip install plotly")

except Exception as e:
    st.error(f"加载数据时出错: {{str(e)}}")
    st.info("请确保数据文件存在且格式正确")
'''

    # 写入文件
    with open(app_file, 'w', encoding='utf-8') as f:
        f.write(app_code)

    return app_file, data_file

if __name__ == "__main__":
    print("创建测试数据...")
    print(df)
    
    print("\n生成包含独立图表可视化功能的streamlit应用...")
    app_file, data_file = create_enhanced_streamlit_app(df, "独立图表可视化测试")
    
    if app_file:
        print(f"应用文件已创建: {app_file}")
        print(f"数据文件已创建: {data_file}")
        print("\n测试完成！可以运行以下命令启动测试应用:")
        print(f"streamlit run \"{app_file}\" --server.port 8503")
    else:
        print("创建应用文件失败！")
