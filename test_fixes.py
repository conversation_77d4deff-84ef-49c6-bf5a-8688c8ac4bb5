#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试修复后的功能
"""
import pandas as pd
import numpy as np

# 创建测试数据
np.random.seed(42)
test_data = {
    '姓名': ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十'] * 3,
    '年龄': np.random.randint(22, 60, 24),
    '城市': np.random.choice(['北京', '上海', '广州', '深圳'], 24),
    '薪资': np.random.normal(10000, 3000, 24).astype(int),
    '部门': np.random.choice(['技术', '销售', '管理'], 24),
    '工作年限': np.random.randint(1, 15, 24),
    '绩效得分': np.random.normal(80, 15, 24),
}

df = pd.DataFrame(test_data)

print("测试数据创建完成:")
print(f"数据形状: {df.shape}")
print(f"数值列: {df.select_dtypes(include=['number']).columns.tolist()}")
print(f"分类列: {df.select_dtypes(include=['object']).columns.tolist()}")

# 保存测试数据
df.to_csv('test_fixes_data.csv', index=False, encoding='utf-8-sig')
print("\n测试数据已保存为 test_fixes_data.csv")

print("\n数据预览:")
print(df.head())

print("\n修复验证要点:")
print("1. 页面卡死问题 - 已添加缓存机制和防抖处理")
print("2. 透视表列名错误 - count聚合时使用行索引列而非第一列")
print("3. 代码健壮性 - 添加了超时处理、错误恢复、状态管理")
print("4. 图表配置问题 - 修复了饼图、帕累托图等配置选项")
print("5. Windows兼容性 - 改进了超时处理机制")

print("\n请使用以下步骤测试:")
print("1. 启动程序: python local_ai_fixedV0.83.py")
print("2. 加载 test_fixes_data.csv 文件")
print("3. 点击'数据预览'")
print("4. 测试交互式图表可视化功能")
print("5. 测试交互式数据透视表功能")
print("6. 验证页面不再卡死，配置选项正常工作")
