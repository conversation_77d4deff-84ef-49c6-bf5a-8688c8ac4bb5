#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试streamlit数据预览功能
"""
import pandas as pd
import tempfile
import os
from datetime import datetime

# 创建测试数据
test_data = {
    '姓名': ['张三', '李四', '王五', '赵六'],
    '年龄': [25, 30, 28, 35],
    '城市': ['北京', '上海', '广州', '深圳'],
    '薪资': [8000, 12000, 9500, 15000]
}
df = pd.DataFrame(test_data)

# 生成streamlit应用代码（简化版）
def create_streamlit_app_file(df, title="数据预览"):
    """创建临时的streamlit应用文件"""
    if df is None:
        return None, None

    # 创建临时文件
    temp_dir = tempfile.gettempdir()
    session_id = "test123"
    app_file = os.path.join(temp_dir, f"streamlit_test_{session_id}.py")
    data_file = os.path.join(temp_dir, f"data_test_{session_id}.pkl")

    # 保存数据到临时文件
    df.to_pickle(data_file)

    # 创建streamlit应用代码
    app_code = f'''
import streamlit as st
import pandas as pd
import numpy as np
import os
import io
try:
    import plotly.express as px
    import plotly.graph_objects as go
    from plotly.subplots import make_subplots
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False
import matplotlib.pyplot as plt
import matplotlib
import base64
from io import BytesIO
from datetime import datetime

# 设置matplotlib后端
matplotlib.use('Agg')

st.set_page_config(
    page_title="{title}",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 加载数据
@st.cache_data
def load_data():
    return pd.read_pickle(r"{data_file}")

try:
    df = load_data()

    # 主标题
    st.title("📊 {title}")
    
    # 显示模块可用性警告
    if not PLOTLY_AVAILABLE:
        st.warning("⚠️ plotly模块未安装，部分交互式图表功能将不可用")
    
    st.markdown("---")

    # 数据概览
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("总行数", df.shape[0])
    with col2:
        st.metric("总列数", df.shape[1])
    with col3:
        st.metric("数值列", len(df.select_dtypes(include=['number']).columns))
    with col4:
        st.metric("文本列", len(df.select_dtypes(include=['object']).columns))

    # 显示数据
    st.subheader("📋 数据内容")
    st.dataframe(df, use_container_width=True)

    # 基本统计信息
    st.subheader("📊 基本统计")
    numeric_df = df.select_dtypes(include=['number'])
    if not numeric_df.empty:
        st.dataframe(numeric_df.describe(), use_container_width=True)
    else:
        st.info("没有数值列可以进行统计分析")

    # 数据类型信息
    st.subheader("🔍 数据类型")
    dtype_info = pd.DataFrame({{
        '列名': df.columns,
        '数据类型': [str(dtype) for dtype in df.dtypes],
        '非空值数量': [df[col].count() for col in df.columns],
        '空值数量': [df[col].isnull().sum() for col in df.columns]
    }})
    st.dataframe(dtype_info, use_container_width=True)

except Exception as e:
    st.error(f"加载数据时出错: {{str(e)}}")
    st.info("请检查数据文件是否存在且格式正确")
'''

    # 写入文件
    with open(app_file, 'w', encoding='utf-8') as f:
        f.write(app_code)

    return app_file, data_file

if __name__ == "__main__":
    print("创建测试数据...")
    print(df)
    
    print("\n生成streamlit应用文件...")
    app_file, data_file = create_streamlit_app_file(df, "测试数据预览")
    
    if app_file:
        print(f"应用文件已创建: {app_file}")
        print(f"数据文件已创建: {data_file}")
        
        # 检查文件内容
        with open(app_file, 'r', encoding='utf-8') as f:
            content = f.read()
            print(f"\n应用文件内容长度: {len(content)} 字符")
            print("应用文件前200个字符:")
            print(content[:200])
            
        print("\n测试完成！")
    else:
        print("创建应用文件失败！")
